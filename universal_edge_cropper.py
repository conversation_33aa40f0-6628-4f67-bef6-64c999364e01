#!/usr/bin/env python3
"""
Universal Edge Cropper - Perfect Edge Detection for Any Image Size
================================================================

This script removes black/dark padding from all edges (top, bottom, left, right)
while preserving 100% of the content pixels. Works with images of any size and
automatically adapts parameters based on image dimensions.

Features:
- Adaptive thresholding based on image size
- Multi-scale content detection
- Robust edge scanning with fallback mechanisms
- Configurable sensitivity levels
- Batch processing support
- Detailed analysis and reporting

Author: AI Assistant
Version: 2.0.0
"""

import cv2
import numpy as np
import argparse
import logging
from pathlib import Path
import time
import json
from typing import Tuple, Dict, List, Optional
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalEdgeCropper:
    """Universal edge cropper that adapts to any image size"""
    
    def __init__(self, 
                 base_threshold: int = 30,
                 content_ratio_threshold: float = 0.1,
                 adaptive_mode: bool = True,
                 sensitivity: str = 'medium'):
        """
        Initialize the Universal Edge Cropper
        
        Args:
            base_threshold: Base threshold for black pixel detection
            content_ratio_threshold: Minimum ratio of content pixels in row/column
            adaptive_mode: Whether to adapt parameters based on image size
            sensitivity: Detection sensitivity ('low', 'medium', 'high', 'ultra')
        """
        self.base_threshold = base_threshold
        self.content_ratio_threshold = content_ratio_threshold
        self.adaptive_mode = adaptive_mode
        self.sensitivity = sensitivity
        
        # Sensitivity configurations
        self.sensitivity_configs = {
            'low': {'ratio': 0.15, 'threshold_mult': 1.2},
            'medium': {'ratio': 0.1, 'threshold_mult': 1.0},
            'high': {'ratio': 0.05, 'threshold_mult': 0.8},
            'ultra': {'ratio': 0.02, 'threshold_mult': 0.6}
        }
        
        # Apply sensitivity settings
        config = self.sensitivity_configs.get(sensitivity, self.sensitivity_configs['medium'])
        self.content_ratio_threshold = config['ratio']
        self.threshold_multiplier = config['threshold_mult']
    
    def process_image(self, image_path: str, output_path: str = None) -> str:
        """
        Process a single image to remove edge padding
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load and validate image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Adapt parameters based on image size
            adapted_params = self._adapt_parameters_to_image_size(image)
            
            # Find content boundaries using adaptive scanning
            content_bounds = self._scan_all_edges_adaptive(image, adapted_params)
            
            # Validate and refine boundaries
            refined_bounds = self._refine_content_boundaries(image, content_bounds)
            
            # Crop to the detected content boundaries
            cropped_image = self._crop_to_content_bounds(image, refined_bounds)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                suffix = f"UNIVERSAL_CROPPED_{self.sensitivity.upper()}_{timestamp}"
                output_path = input_path.parent / f"{input_path.stem}_{suffix}.jpg"
            
            # Save with maximum quality
            cv2.imwrite(str(output_path), cropped_image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            # Log results
            self._log_processing_results(image, cropped_image, refined_bounds, output_path)
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            raise
    
    def _adapt_parameters_to_image_size(self, image: np.ndarray) -> Dict:
        """Adapt processing parameters based on image dimensions"""
        try:
            height, width = image.shape[:2]
            total_pixels = height * width
            
            # Calculate adaptive parameters
            params = {
                'black_threshold': int(self.base_threshold * self.threshold_multiplier),
                'content_ratio': self.content_ratio_threshold,
                'scan_step': 1,  # Always scan every pixel for precision
                'min_content_pixels': max(1, int(total_pixels * 0.0001))  # 0.01% minimum
            }
            
            # Adjust for very large images (>4MP)
            if total_pixels > 4000000:
                params['scan_step'] = 2  # Skip every other pixel for speed
                params['content_ratio'] *= 0.8  # More sensitive for large images
            
            # Adjust for very small images (<100K pixels)
            elif total_pixels < 100000:
                params['content_ratio'] *= 1.5  # Less sensitive for small images
                params['black_threshold'] = max(20, params['black_threshold'])
            
            logger.debug(f"Adapted parameters for {width}x{height}: {params}")
            return params
            
        except Exception as e:
            logger.error(f"Error adapting parameters: {e}")
            return {
                'black_threshold': self.base_threshold,
                'content_ratio': self.content_ratio_threshold,
                'scan_step': 1,
                'min_content_pixels': 1
            }
    
    def _scan_all_edges_adaptive(self, image: np.ndarray, params: Dict) -> Tuple[int, int, int, int]:
        """
        Scan from all edges using advanced multi-method detection
        Returns (left, top, width, height) of content area
        """
        try:
            height, width = image.shape[:2]

            # Method 1: Traditional edge scanning with enhanced detection
            left_bound_traditional = self._scan_from_left_adaptive(image, params)
            right_bound_traditional = self._scan_from_right_adaptive(image, params)
            top_bound_traditional = self._scan_from_top_adaptive(image, params)
            bottom_bound_traditional = self._scan_from_bottom_adaptive(image, params)

            # Method 2: Mask-based detection for more accurate results
            mask_bounds = self._detect_content_using_mask(image, params)

            # Method 3: Combine results intelligently
            if mask_bounds is not None:
                mask_left, mask_top, mask_width, mask_height = mask_bounds
                mask_right = mask_left + mask_width
                mask_bottom = mask_top + mask_height

                # Use the most restrictive (inner) bounds for better accuracy
                left_bound = max(left_bound_traditional, mask_left)
                right_bound = min(right_bound_traditional, mask_right)
                top_bound = max(top_bound_traditional, mask_top)
                bottom_bound = min(bottom_bound_traditional, mask_bottom)

                # Ensure bounds are valid
                if left_bound >= right_bound:
                    left_bound = left_bound_traditional
                    right_bound = right_bound_traditional
                if top_bound >= bottom_bound:
                    top_bound = top_bound_traditional
                    bottom_bound = bottom_bound_traditional

                logger.info(f"Combined traditional + mask detection results")
            else:
                # Fall back to traditional method
                left_bound = left_bound_traditional
                right_bound = right_bound_traditional
                top_bound = top_bound_traditional
                bottom_bound = bottom_bound_traditional
                logger.info(f"Using traditional edge detection (mask detection failed)")

            # Calculate content area
            content_left = left_bound
            content_top = top_bound
            content_width = max(1, right_bound - left_bound)
            content_height = max(1, bottom_bound - top_bound)

            logger.info(f"Final content boundaries:")
            logger.info(f"  Left: {left_bound}, Right: {right_bound}")
            logger.info(f"  Top: {top_bound}, Bottom: {bottom_bound}")
            logger.info(f"  Content area: {content_width}x{content_height}")

            return (content_left, content_top, content_width, content_height)

        except Exception as e:
            logger.error(f"Error in adaptive edge scanning: {e}")
            return (0, 0, image.shape[1], image.shape[0])

    def _detect_content_using_mask(self, image: np.ndarray, params: Dict) -> Optional[Tuple[int, int, int, int]]:
        """Detect content boundaries using advanced mask-based detection"""
        try:
            # Create comprehensive content mask
            content_mask = self._create_comprehensive_content_mask(image, params)

            if content_mask is None:
                return None

            # Find bounding box of all content pixels
            points = cv2.findNonZero(content_mask)
            if points is None:
                return None

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(points)

            logger.debug(f"Mask-based detection found content at: x={x}, y={y}, w={w}, h={h}")

            return (x, y, w, h)

        except Exception as e:
            logger.error(f"Error in mask-based detection: {e}")
            return None

    def _create_comprehensive_content_mask(self, image: np.ndarray, params: Dict) -> Optional[np.ndarray]:
        """Create comprehensive content mask using multiple detection methods"""
        try:
            # Convert to different color spaces
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Method 1: Black background detection
            black_mask = self._detect_black_background_mask(image, gray, params)

            # Method 2: Color-based bird detection
            color_mask = self._detect_bird_by_color_mask(hsv, params)

            # Method 3: Texture-based detection
            texture_mask = self._detect_bird_by_texture_mask(gray, params)

            # Method 4: Edge-based detection
            edge_mask = self._detect_bird_by_edges_mask(gray, params)

            # Combine masks intelligently
            valid_masks = [mask for mask in [color_mask, texture_mask, edge_mask] if mask is not None]

            if not valid_masks:
                logger.warning("No valid content masks generated")
                return None

            # Start with first valid mask
            combined_mask = valid_masks[0].copy()

            # Combine using OR operation (union of all detections)
            for mask in valid_masks[1:]:
                combined_mask = cv2.bitwise_or(combined_mask, mask)

            # Remove black background areas
            if black_mask is not None:
                final_mask = cv2.bitwise_and(combined_mask, cv2.bitwise_not(black_mask))
            else:
                final_mask = combined_mask

            # Clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)
            final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)

            # Remove small noise regions
            contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            noise_threshold = params.get('min_content_pixels', 100)

            # Filter contours by area
            valid_contours = [c for c in contours if cv2.contourArea(c) > noise_threshold]

            if valid_contours:
                # Create clean mask with only valid contours
                clean_mask = np.zeros_like(final_mask)
                cv2.fillPoly(clean_mask, valid_contours, 255)
                final_mask = clean_mask

            logger.debug(f"Content mask contains {np.sum(final_mask > 0)} content pixels")

            return final_mask

        except Exception as e:
            logger.error(f"Error creating comprehensive content mask: {e}")
            return None
    
    def _scan_from_left_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from left edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for x in range(0, width, step):
                column = image[:, x]
                if self._has_content_adaptive(column, params, 'column'):
                    logger.debug(f"Left content starts at column {x}")
                    return x
            
            return 0
            
        except Exception as e:
            logger.error(f"Error scanning from left: {e}")
            return 0
    
    def _scan_from_right_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from right edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for x in range(width - 1, -1, -step):
                column = image[:, x]
                if self._has_content_adaptive(column, params, 'column'):
                    logger.debug(f"Right content ends at column {x + 1}")
                    return x + 1
            
            return width
            
        except Exception as e:
            logger.error(f"Error scanning from right: {e}")
            return image.shape[1]
    
    def _scan_from_top_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from top edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for y in range(0, height, step):
                row = image[y, :]
                if self._has_content_adaptive(row, params, 'row'):
                    logger.debug(f"Top content starts at row {y}")
                    return y
            
            return 0
            
        except Exception as e:
            logger.error(f"Error scanning from top: {e}")
            return 0
    
    def _scan_from_bottom_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from bottom edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for y in range(height - 1, -1, -step):
                row = image[y, :]
                if self._has_content_adaptive(row, params, 'row'):
                    logger.debug(f"Bottom content ends at row {y + 1}")
                    return y + 1
            
            return height
            
        except Exception as e:
            logger.error(f"Error scanning from bottom: {e}")
            return image.shape[0]
    
    def _has_content_adaptive(self, pixels: np.ndarray, params: Dict, direction: str) -> bool:
        """Check if pixels have significant content using advanced multi-method detection"""
        try:
            # Convert to proper format for analysis
            if len(pixels.shape) == 3:
                if direction == 'column':
                    # Reshape column pixels for processing
                    pixel_strip = pixels.reshape(-1, 1, 3)
                else:  # row
                    # Reshape row pixels for processing
                    pixel_strip = pixels.reshape(1, -1, 3)
            else:
                # Already grayscale
                if direction == 'column':
                    pixel_strip = pixels.reshape(-1, 1)
                else:
                    pixel_strip = pixels.reshape(1, -1)
                # Convert to 3-channel for consistency
                pixel_strip = cv2.cvtColor(pixel_strip, cv2.COLOR_GRAY2BGR)

            # Method 1: Advanced black background detection
            has_content_black = self._detect_content_vs_black_background(pixel_strip, params)

            # Method 2: Color-based bird content detection
            has_content_color = self._detect_bird_content_by_color(pixel_strip, params)

            # Method 3: Texture-based content detection
            has_content_texture = self._detect_content_by_texture(pixel_strip, params)

            # Combine detection methods (any method detecting content = has content)
            has_content = has_content_black or has_content_color or has_content_texture

            return has_content

        except Exception as e:
            logger.error(f"Error checking content: {e}")
            return True  # Conservative: assume it has content

    def _detect_content_vs_black_background(self, pixel_strip: np.ndarray, params: Dict) -> bool:
        """Detect content vs black background using advanced thresholding"""
        try:
            # Convert to grayscale for analysis
            if len(pixel_strip.shape) == 3:
                gray_strip = cv2.cvtColor(pixel_strip, cv2.COLOR_BGR2GRAY)
            else:
                gray_strip = pixel_strip

            # Method 1: Simple threshold
            content_pixels_simple = np.sum(gray_strip > params['black_threshold'])

            # Method 2: Multi-channel analysis for color images
            if len(pixel_strip.shape) == 3:
                b, g, r = cv2.split(pixel_strip)

                # Check each channel separately
                content_b = np.sum(b > params['black_threshold'])
                content_g = np.sum(g > params['black_threshold'])
                content_r = np.sum(r > params['black_threshold'])

                # Content if any channel has significant non-black pixels
                content_pixels_color = max(content_b, content_g, content_r)
            else:
                content_pixels_color = content_pixels_simple

            # Use the maximum detection
            content_pixels = max(content_pixels_simple, content_pixels_color)
            total_pixels = gray_strip.size

            # Check both ratio and absolute count
            content_ratio = content_pixels / total_pixels if total_pixels > 0 else 0
            has_enough_ratio = content_ratio > params['content_ratio']
            has_min_pixels = content_pixels >= params['min_content_pixels']

            return has_enough_ratio and has_min_pixels

        except Exception as e:
            logger.error(f"Error in black background detection: {e}")
            return False

    def _detect_bird_content_by_color(self, pixel_strip: np.ndarray, params: Dict) -> bool:
        """Detect bird content using color analysis"""
        try:
            if len(pixel_strip.shape) != 3:
                return False

            # Convert to HSV for better color analysis
            hsv_strip = cv2.cvtColor(pixel_strip, cv2.COLOR_BGR2HSV)

            # Bird color ranges (from zero_background_cropper.py)
            bird_ranges = [
                # Brown/tan birds
                ([5, 20, 30], [30, 255, 255]),
                # Gray birds
                ([0, 0, 40], [180, 50, 200]),
                # Dark birds (but not black background)
                ([0, 0, 35], [180, 255, 120]),
                # Colorful birds (red, orange, yellow)
                ([0, 50, 50], [35, 255, 255]),
                # Blue birds
                ([90, 30, 30], [130, 255, 255]),
                # Green birds
                ([35, 30, 30], [85, 255, 255]),
                # White/light birds
                ([0, 0, 150], [180, 50, 255])
            ]

            # Check if pixels match any bird color range
            bird_pixels = 0
            for lower, upper in bird_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv_strip, lower, upper)
                bird_pixels += np.sum(mask > 0)

            total_pixels = hsv_strip.shape[0] * hsv_strip.shape[1]
            bird_ratio = bird_pixels / total_pixels if total_pixels > 0 else 0

            # Lower threshold for color detection (more sensitive)
            return bird_ratio > (params['content_ratio'] * 0.5)

        except Exception as e:
            logger.error(f"Error in color-based detection: {e}")
            return False

    def _detect_content_by_texture(self, pixel_strip: np.ndarray, params: Dict) -> bool:
        """Detect content using texture analysis"""
        try:
            # Convert to grayscale
            if len(pixel_strip.shape) == 3:
                gray_strip = cv2.cvtColor(pixel_strip, cv2.COLOR_BGR2GRAY)
            else:
                gray_strip = pixel_strip

            # Skip texture analysis for very small strips
            if gray_strip.size < 25:  # Less than 5x5 pixels
                return False

            # Calculate local variance (texture measure)
            if len(gray_strip.shape) == 2 and min(gray_strip.shape) >= 3:
                # For 2D strips, calculate variance
                variance = np.var(gray_strip.astype(np.float32))

                # Adaptive threshold based on image characteristics
                texture_threshold = 20 * params.get('threshold_multiplier', 1.0)

                return variance > texture_threshold
            else:
                # For 1D strips, calculate standard deviation
                std_dev = np.std(gray_strip.astype(np.float32))
                return std_dev > 10  # Threshold for texture variation

        except Exception as e:
            logger.error(f"Error in texture-based detection: {e}")
            return False

    def _detect_black_background_mask(self, image: np.ndarray, gray: np.ndarray, params: Dict) -> Optional[np.ndarray]:
        """Detect black/dark background pixels and return mask"""
        try:
            black_threshold = params['black_threshold']

            # Create mask for very dark pixels
            _, black_mask = cv2.threshold(gray, black_threshold, 255, cv2.THRESH_BINARY_INV)

            # Also check for near-black colors in BGR
            b, g, r = cv2.split(image)

            # Pixels where all channels are below threshold
            dark_b = b < black_threshold
            dark_g = g < black_threshold
            dark_r = r < black_threshold

            # Combine all dark channel conditions
            all_dark = np.logical_and(np.logical_and(dark_b, dark_g), dark_r).astype(np.uint8) * 255

            # Combine grayscale and color-based black detection
            combined_black = cv2.bitwise_or(black_mask, all_dark)

            # Clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            combined_black = cv2.morphologyEx(combined_black, cv2.MORPH_CLOSE, kernel)

            return combined_black

        except Exception as e:
            logger.error(f"Error detecting black background mask: {e}")
            return None

    def _detect_bird_by_color_mask(self, hsv: np.ndarray, params: Dict) -> Optional[np.ndarray]:
        """Detect bird using color analysis and return mask"""
        try:
            # Comprehensive bird color ranges
            bird_ranges = [
                # Brown/tan birds
                ([5, 20, 30], [30, 255, 255]),
                # Gray birds
                ([0, 0, 40], [180, 50, 200]),
                # Dark birds (but not black background)
                ([0, 0, 35], [180, 255, 120]),
                # Colorful birds (red, orange, yellow)
                ([0, 50, 50], [35, 255, 255]),
                # Blue birds
                ([90, 30, 30], [130, 255, 255]),
                # Green birds
                ([35, 30, 30], [85, 255, 255]),
                # White/light birds
                ([0, 0, 150], [180, 50, 255])
            ]

            # Create combined color mask
            color_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)

            for lower, upper in bird_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv, lower, upper)
                color_mask = cv2.bitwise_or(color_mask, mask)

            # Clean up color mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_OPEN, kernel)
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_CLOSE, kernel)

            return color_mask

        except Exception as e:
            logger.error(f"Error in color-based mask detection: {e}")
            return None

    def _detect_bird_by_texture_mask(self, gray: np.ndarray, params: Dict) -> Optional[np.ndarray]:
        """Detect bird using texture analysis and return mask"""
        try:
            # Calculate local variance (texture measure)
            kernel_size = 5
            kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)

            # Convert to float for calculations
            gray_float = gray.astype(np.float32)

            # Calculate local mean and variance
            local_mean = cv2.filter2D(gray_float, -1, kernel)
            local_variance = cv2.filter2D((gray_float - local_mean)**2, -1, kernel)

            # Normalize variance
            variance_norm = cv2.normalize(local_variance, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

            # Adaptive threshold based on parameters
            texture_threshold = int(20 * params.get('threshold_multiplier', 1.0))

            # Threshold to get textured regions
            _, texture_mask = cv2.threshold(variance_norm, texture_threshold, 255, cv2.THRESH_BINARY)

            return texture_mask

        except Exception as e:
            logger.error(f"Error in texture-based mask detection: {e}")
            return None

    def _detect_bird_by_edges_mask(self, gray: np.ndarray, params: Dict) -> Optional[np.ndarray]:
        """Detect bird using edge analysis and return mask"""
        try:
            # Apply bilateral filter to preserve edges while reducing noise
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)

            # Adaptive edge thresholds based on parameters
            base_threshold = params['black_threshold']
            threshold_mult = params.get('threshold_multiplier', 1.0)

            # Multi-scale edge detection with adaptive thresholds
            low1, high1 = int(20 * threshold_mult), int(60 * threshold_mult)
            low2, high2 = int(40 * threshold_mult), int(100 * threshold_mult)
            low3, high3 = int(60 * threshold_mult), int(150 * threshold_mult)

            edges1 = cv2.Canny(filtered, low1, high1)
            edges2 = cv2.Canny(filtered, low2, high2)
            edges3 = cv2.Canny(filtered, low3, high3)

            # Combine edges
            combined_edges = cv2.bitwise_or(edges1, edges2)
            combined_edges = cv2.bitwise_or(combined_edges, edges3)

            # Dilate edges to create regions
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            edge_regions = cv2.dilate(combined_edges, kernel, iterations=2)

            return edge_regions

        except Exception as e:
            logger.error(f"Error in edge-based mask detection: {e}")
            return None

    def _refine_content_boundaries(self, image: np.ndarray, bounds: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """Refine content boundaries to ensure they're valid"""
        try:
            left, top, width, height = bounds
            img_height, img_width = image.shape[:2]

            # Ensure boundaries are within image limits
            left = max(0, min(left, img_width - 1))
            top = max(0, min(top, img_height - 1))
            right = min(img_width, left + width)
            bottom = min(img_height, top + height)

            # Ensure minimum size (at least 1x1)
            if right <= left:
                right = left + 1
            if bottom <= top:
                bottom = top + 1

            # Recalculate width and height
            final_width = right - left
            final_height = bottom - top

            logger.debug(f"Refined boundaries: left={left}, top={top}, width={final_width}, height={final_height}")

            return (left, top, final_width, final_height)

        except Exception as e:
            logger.error(f"Error refining boundaries: {e}")
            return bounds

    def _crop_to_content_bounds(self, image: np.ndarray, bounds: Tuple[int, int, int, int]) -> np.ndarray:
        """Crop image to the specified content bounds"""
        try:
            left, top, width, height = bounds

            # Calculate right and bottom coordinates
            right = left + width
            bottom = top + height

            # Crop the image
            cropped = image[top:bottom, left:right]

            logger.debug(f"Cropped from {image.shape[1]}x{image.shape[0]} to {cropped.shape[1]}x{cropped.shape[0]}")

            return cropped

        except Exception as e:
            logger.error(f"Error cropping to bounds: {e}")
            return image

    def _log_processing_results(self, original: np.ndarray, cropped: np.ndarray, bounds: Tuple[int, int, int, int], output_path: str):
        """Log detailed processing results"""
        try:
            left, top, width, height = bounds
            original_h, original_w = original.shape[:2]
            final_h, final_w = cropped.shape[:2]

            # Calculate padding removed
            top_removed = top
            left_removed = left
            bottom_removed = original_h - (top + height)
            right_removed = original_w - (left + width)

            # Calculate statistics
            original_pixels = original_w * original_h
            final_pixels = final_w * final_h
            pixels_removed = original_pixels - final_pixels
            reduction_percent = (pixels_removed / original_pixels) * 100

            logger.info(f"✅ Processing completed successfully!")
            logger.info(f"📁 Output saved: {output_path}")
            logger.info(f"📏 Size change: {original_w}x{original_h} → {final_w}x{final_h}")
            logger.info(f"🗑️  Padding removed - Top: {top_removed}, Bottom: {bottom_removed}, Left: {left_removed}, Right: {right_removed}")
            logger.info(f"📊 Total pixels removed: {pixels_removed:,} ({reduction_percent:.1f}%)")

        except Exception as e:
            logger.error(f"Error logging results: {e}")

    def analyze_image_padding(self, image_path: str) -> Dict:
        """Analyze padding on all sides without processing"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return {"error": "Could not load image"}

            height, width = image.shape[:2]
            adapted_params = self._adapt_parameters_to_image_size(image)

            # Scan each edge
            left_bound = self._scan_from_left_adaptive(image, adapted_params)
            right_bound = self._scan_from_right_adaptive(image, adapted_params)
            top_bound = self._scan_from_top_adaptive(image, adapted_params)
            bottom_bound = self._scan_from_bottom_adaptive(image, adapted_params)

            # Calculate padding and final dimensions
            left_padding = left_bound
            right_padding = width - right_bound
            top_padding = top_bound
            bottom_padding = height - bottom_bound

            final_width = right_bound - left_bound
            final_height = bottom_bound - top_bound

            total_padding = (width * height) - (final_width * final_height)
            reduction_percent = (total_padding / (width * height)) * 100

            return {
                "original_size": f"{width}x{height}",
                "final_size": f"{final_width}x{final_height}",
                "left_padding": left_padding,
                "right_padding": right_padding,
                "top_padding": top_padding,
                "bottom_padding": bottom_padding,
                "total_padding_pixels": total_padding,
                "size_reduction_percent": round(reduction_percent, 2),
                "sensitivity_used": self.sensitivity,
                "parameters_used": adapted_params
            }

        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            return {"error": str(e)}

    def batch_process(self, input_dir: str, output_dir: str = None, file_extensions: List[str] = None) -> Dict:
        """Process multiple images in a directory"""
        try:
            if file_extensions is None:
                file_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']

            input_path = Path(input_dir)
            if not input_path.exists():
                raise ValueError(f"Input directory does not exist: {input_dir}")

            # Create output directory
            if output_dir is None:
                output_dir = input_path / "cropped_universal"
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)

            # Find all image files
            image_files = []
            for ext in file_extensions:
                image_files.extend(input_path.glob(f"*{ext}"))
                image_files.extend(input_path.glob(f"*{ext.upper()}"))

            if not image_files:
                logger.warning(f"No image files found in {input_dir}")
                return {"processed": 0, "failed": 0, "results": []}

            logger.info(f"Found {len(image_files)} images to process")

            # Process each image
            results = []
            processed = 0
            failed = 0

            for i, img_file in enumerate(image_files, 1):
                try:
                    logger.info(f"Processing {i}/{len(image_files)}: {img_file.name}")

                    # Generate output path
                    output_file = output_path / f"{img_file.stem}_universal_cropped{img_file.suffix}"

                    # Process image
                    result_path = self.process_image(str(img_file), str(output_file))

                    results.append({
                        "input": str(img_file),
                        "output": result_path,
                        "status": "success"
                    })
                    processed += 1

                except Exception as e:
                    logger.error(f"Failed to process {img_file}: {e}")
                    results.append({
                        "input": str(img_file),
                        "output": None,
                        "status": "failed",
                        "error": str(e)
                    })
                    failed += 1

            # Save batch report
            report = {
                "processed": processed,
                "failed": failed,
                "total": len(image_files),
                "sensitivity": self.sensitivity,
                "output_directory": str(output_path),
                "results": results
            }

            report_file = output_path / f"batch_report_{int(time.time())}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            logger.info(f"✅ Batch processing completed!")
            logger.info(f"📊 Processed: {processed}, Failed: {failed}")
            logger.info(f"📁 Report saved: {report_file}")

            return report

        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            raise


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="Universal Edge Cropper - Remove padding from images of any size",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage
  python universal_edge_cropper.py input.jpg

  # High sensitivity cropping
  python universal_edge_cropper.py input.jpg -s high

  # Batch process directory
  python universal_edge_cropper.py /path/to/images --batch

  # Analyze padding only
  python universal_edge_cropper.py input.jpg --analyze-only

  # Custom output path
  python universal_edge_cropper.py input.jpg -o output.jpg
        """
    )

    parser.add_argument('input', help='Input image file or directory (for batch mode)')
    parser.add_argument('-o', '--output', help='Output image path (for single image)')
    parser.add_argument('-s', '--sensitivity', choices=['low', 'medium', 'high', 'ultra'],
                       default='medium', help='Detection sensitivity level')
    parser.add_argument('-t', '--threshold', type=int, default=30,
                       help='Base threshold for black pixel detection')
    parser.add_argument('--batch', action='store_true',
                       help='Process all images in input directory')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze padding without processing')
    parser.add_argument('--output-dir', help='Output directory for batch processing')
    parser.add_argument('--no-adaptive', action='store_true',
                       help='Disable adaptive parameter adjustment')

    args = parser.parse_args()

    try:
        # Create cropper instance
        cropper = UniversalEdgeCropper(
            base_threshold=args.threshold,
            adaptive_mode=not args.no_adaptive,
            sensitivity=args.sensitivity
        )

        if args.analyze_only:
            # Analyze image padding
            analysis = cropper.analyze_image_padding(args.input)
            print("\n" + "="*70)
            print("UNIVERSAL EDGE PADDING ANALYSIS")
            print("="*70)
            for key, value in analysis.items():
                if key != "parameters_used":
                    print(f"{key.replace('_', ' ').title()}: {value}")
            print("="*70)

        elif args.batch:
            # Batch process directory
            report = cropper.batch_process(args.input, args.output_dir)
            print(f"\n✅ Batch processing completed!")
            print(f"📊 Results: {report['processed']} processed, {report['failed']} failed")

        else:
            # Process single image
            result = cropper.process_image(args.input, args.output)
            print(f"\n✅ Successfully processed: {result}")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
