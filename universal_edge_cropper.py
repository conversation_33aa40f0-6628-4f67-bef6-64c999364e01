#!/usr/bin/env python3
"""
Universal Edge Cropper - Perfect Edge Detection for Any Image Size
================================================================

This script removes black/dark padding from all edges (top, bottom, left, right)
while preserving 100% of the content pixels. Works with images of any size and
automatically adapts parameters based on image dimensions.

Features:
- Adaptive thresholding based on image size
- Multi-scale content detection
- Robust edge scanning with fallback mechanisms
- Configurable sensitivity levels
- Batch processing support
- Detailed analysis and reporting

Author: AI Assistant
Version: 2.0.0
"""

import cv2
import numpy as np
import argparse
import logging
from pathlib import Path
import time
import json
from typing import Tuple, Dict, List, Optional
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalEdgeCropper:
    """Universal edge cropper that adapts to any image size"""
    
    def __init__(self, 
                 base_threshold: int = 30,
                 content_ratio_threshold: float = 0.1,
                 adaptive_mode: bool = True,
                 sensitivity: str = 'medium'):
        """
        Initialize the Universal Edge Cropper
        
        Args:
            base_threshold: Base threshold for black pixel detection
            content_ratio_threshold: Minimum ratio of content pixels in row/column
            adaptive_mode: Whether to adapt parameters based on image size
            sensitivity: Detection sensitivity ('low', 'medium', 'high', 'ultra')
        """
        self.base_threshold = base_threshold
        self.content_ratio_threshold = content_ratio_threshold
        self.adaptive_mode = adaptive_mode
        self.sensitivity = sensitivity
        
        # Sensitivity configurations
        self.sensitivity_configs = {
            'low': {'ratio': 0.15, 'threshold_mult': 1.2},
            'medium': {'ratio': 0.1, 'threshold_mult': 1.0},
            'high': {'ratio': 0.05, 'threshold_mult': 0.8},
            'ultra': {'ratio': 0.02, 'threshold_mult': 0.6}
        }
        
        # Apply sensitivity settings
        config = self.sensitivity_configs.get(sensitivity, self.sensitivity_configs['medium'])
        self.content_ratio_threshold = config['ratio']
        self.threshold_multiplier = config['threshold_mult']
    
    def process_image(self, image_path: str, output_path: str = None) -> str:
        """
        Process a single image to remove edge padding
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load and validate image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Adapt parameters based on image size
            adapted_params = self._adapt_parameters_to_image_size(image)
            
            # Find content boundaries using adaptive scanning
            content_bounds = self._scan_all_edges_adaptive(image, adapted_params)
            
            # Validate and refine boundaries
            refined_bounds = self._refine_content_boundaries(image, content_bounds)
            
            # Crop to the detected content boundaries
            cropped_image = self._crop_to_content_bounds(image, refined_bounds)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                suffix = f"UNIVERSAL_CROPPED_{self.sensitivity.upper()}_{timestamp}"
                output_path = input_path.parent / f"{input_path.stem}_{suffix}.jpg"
            
            # Save with maximum quality
            cv2.imwrite(str(output_path), cropped_image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            # Log results
            self._log_processing_results(image, cropped_image, refined_bounds, output_path)
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            raise
    
    def _adapt_parameters_to_image_size(self, image: np.ndarray) -> Dict:
        """Adapt processing parameters based on image dimensions"""
        try:
            height, width = image.shape[:2]
            total_pixels = height * width
            
            # Calculate adaptive parameters
            params = {
                'black_threshold': int(self.base_threshold * self.threshold_multiplier),
                'content_ratio': self.content_ratio_threshold,
                'scan_step': 1,  # Always scan every pixel for precision
                'min_content_pixels': max(1, int(total_pixels * 0.0001))  # 0.01% minimum
            }
            
            # Adjust for very large images (>4MP)
            if total_pixels > 4000000:
                params['scan_step'] = 2  # Skip every other pixel for speed
                params['content_ratio'] *= 0.8  # More sensitive for large images
            
            # Adjust for very small images (<100K pixels)
            elif total_pixels < 100000:
                params['content_ratio'] *= 1.5  # Less sensitive for small images
                params['black_threshold'] = max(20, params['black_threshold'])
            
            logger.debug(f"Adapted parameters for {width}x{height}: {params}")
            return params
            
        except Exception as e:
            logger.error(f"Error adapting parameters: {e}")
            return {
                'black_threshold': self.base_threshold,
                'content_ratio': self.content_ratio_threshold,
                'scan_step': 1,
                'min_content_pixels': 1
            }
    
    def _scan_all_edges_adaptive(self, image: np.ndarray, params: Dict) -> Tuple[int, int, int, int]:
        """
        Scan from all edges using adaptive parameters
        Returns (left, top, width, height) of content area
        """
        try:
            height, width = image.shape[:2]
            
            # Scan from each edge with adaptive parameters
            left_bound = self._scan_from_left_adaptive(image, params)
            right_bound = self._scan_from_right_adaptive(image, params)
            top_bound = self._scan_from_top_adaptive(image, params)
            bottom_bound = self._scan_from_bottom_adaptive(image, params)
            
            # Calculate content area
            content_left = left_bound
            content_top = top_bound
            content_width = max(1, right_bound - left_bound)
            content_height = max(1, bottom_bound - top_bound)
            
            logger.info(f"Adaptive content boundaries detected:")
            logger.info(f"  Left: {left_bound}, Right: {right_bound}")
            logger.info(f"  Top: {top_bound}, Bottom: {bottom_bound}")
            logger.info(f"  Content area: {content_width}x{content_height}")
            
            return (content_left, content_top, content_width, content_height)
            
        except Exception as e:
            logger.error(f"Error in adaptive edge scanning: {e}")
            return (0, 0, image.shape[1], image.shape[0])
    
    def _scan_from_left_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from left edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for x in range(0, width, step):
                column = image[:, x]
                if self._has_content_adaptive(column, params, 'column'):
                    logger.debug(f"Left content starts at column {x}")
                    return x
            
            return 0
            
        except Exception as e:
            logger.error(f"Error scanning from left: {e}")
            return 0
    
    def _scan_from_right_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from right edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for x in range(width - 1, -1, -step):
                column = image[:, x]
                if self._has_content_adaptive(column, params, 'column'):
                    logger.debug(f"Right content ends at column {x + 1}")
                    return x + 1
            
            return width
            
        except Exception as e:
            logger.error(f"Error scanning from right: {e}")
            return image.shape[1]
    
    def _scan_from_top_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from top edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for y in range(0, height, step):
                row = image[y, :]
                if self._has_content_adaptive(row, params, 'row'):
                    logger.debug(f"Top content starts at row {y}")
                    return y
            
            return 0
            
        except Exception as e:
            logger.error(f"Error scanning from top: {e}")
            return 0
    
    def _scan_from_bottom_adaptive(self, image: np.ndarray, params: Dict) -> int:
        """Scan from bottom edge with adaptive parameters"""
        try:
            height, width = image.shape[:2]
            step = params['scan_step']
            
            for y in range(height - 1, -1, -step):
                row = image[y, :]
                if self._has_content_adaptive(row, params, 'row'):
                    logger.debug(f"Bottom content ends at row {y + 1}")
                    return y + 1
            
            return height
            
        except Exception as e:
            logger.error(f"Error scanning from bottom: {e}")
            return image.shape[0]
    
    def _has_content_adaptive(self, pixels: np.ndarray, params: Dict, direction: str) -> bool:
        """Check if pixels have significant content using adaptive parameters"""
        try:
            # Convert to grayscale if needed
            if len(pixels.shape) == 3:
                if direction == 'column':
                    gray_pixels = cv2.cvtColor(pixels.reshape(-1, 1, 3), cv2.COLOR_BGR2GRAY).flatten()
                else:  # row
                    gray_pixels = cv2.cvtColor(pixels.reshape(1, -1, 3), cv2.COLOR_BGR2GRAY).flatten()
            else:
                gray_pixels = pixels.flatten()
            
            # Count pixels above threshold
            content_pixels = np.sum(gray_pixels > params['black_threshold'])
            total_pixels = len(gray_pixels)
            
            # Check both ratio and absolute count
            content_ratio = content_pixels / total_pixels
            has_enough_ratio = content_ratio > params['content_ratio']
            has_min_pixels = content_pixels >= params['min_content_pixels']
            
            return has_enough_ratio and has_min_pixels
            
        except Exception as e:
            logger.error(f"Error checking content: {e}")
            return True  # Conservative: assume it has content

    def _refine_content_boundaries(self, image: np.ndarray, bounds: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """Refine content boundaries to ensure they're valid"""
        try:
            left, top, width, height = bounds
            img_height, img_width = image.shape[:2]

            # Ensure boundaries are within image limits
            left = max(0, min(left, img_width - 1))
            top = max(0, min(top, img_height - 1))
            right = min(img_width, left + width)
            bottom = min(img_height, top + height)

            # Ensure minimum size (at least 1x1)
            if right <= left:
                right = left + 1
            if bottom <= top:
                bottom = top + 1

            # Recalculate width and height
            final_width = right - left
            final_height = bottom - top

            logger.debug(f"Refined boundaries: left={left}, top={top}, width={final_width}, height={final_height}")

            return (left, top, final_width, final_height)

        except Exception as e:
            logger.error(f"Error refining boundaries: {e}")
            return bounds

    def _crop_to_content_bounds(self, image: np.ndarray, bounds: Tuple[int, int, int, int]) -> np.ndarray:
        """Crop image to the specified content bounds"""
        try:
            left, top, width, height = bounds

            # Calculate right and bottom coordinates
            right = left + width
            bottom = top + height

            # Crop the image
            cropped = image[top:bottom, left:right]

            logger.debug(f"Cropped from {image.shape[1]}x{image.shape[0]} to {cropped.shape[1]}x{cropped.shape[0]}")

            return cropped

        except Exception as e:
            logger.error(f"Error cropping to bounds: {e}")
            return image

    def _log_processing_results(self, original: np.ndarray, cropped: np.ndarray, bounds: Tuple[int, int, int, int], output_path: str):
        """Log detailed processing results"""
        try:
            left, top, width, height = bounds
            original_h, original_w = original.shape[:2]
            final_h, final_w = cropped.shape[:2]

            # Calculate padding removed
            top_removed = top
            left_removed = left
            bottom_removed = original_h - (top + height)
            right_removed = original_w - (left + width)

            # Calculate statistics
            original_pixels = original_w * original_h
            final_pixels = final_w * final_h
            pixels_removed = original_pixels - final_pixels
            reduction_percent = (pixels_removed / original_pixels) * 100

            logger.info(f"✅ Processing completed successfully!")
            logger.info(f"📁 Output saved: {output_path}")
            logger.info(f"📏 Size change: {original_w}x{original_h} → {final_w}x{final_h}")
            logger.info(f"🗑️  Padding removed - Top: {top_removed}, Bottom: {bottom_removed}, Left: {left_removed}, Right: {right_removed}")
            logger.info(f"📊 Total pixels removed: {pixels_removed:,} ({reduction_percent:.1f}%)")

        except Exception as e:
            logger.error(f"Error logging results: {e}")

    def analyze_image_padding(self, image_path: str) -> Dict:
        """Analyze padding on all sides without processing"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return {"error": "Could not load image"}

            height, width = image.shape[:2]
            adapted_params = self._adapt_parameters_to_image_size(image)

            # Scan each edge
            left_bound = self._scan_from_left_adaptive(image, adapted_params)
            right_bound = self._scan_from_right_adaptive(image, adapted_params)
            top_bound = self._scan_from_top_adaptive(image, adapted_params)
            bottom_bound = self._scan_from_bottom_adaptive(image, adapted_params)

            # Calculate padding and final dimensions
            left_padding = left_bound
            right_padding = width - right_bound
            top_padding = top_bound
            bottom_padding = height - bottom_bound

            final_width = right_bound - left_bound
            final_height = bottom_bound - top_bound

            total_padding = (width * height) - (final_width * final_height)
            reduction_percent = (total_padding / (width * height)) * 100

            return {
                "original_size": f"{width}x{height}",
                "final_size": f"{final_width}x{final_height}",
                "left_padding": left_padding,
                "right_padding": right_padding,
                "top_padding": top_padding,
                "bottom_padding": bottom_padding,
                "total_padding_pixels": total_padding,
                "size_reduction_percent": round(reduction_percent, 2),
                "sensitivity_used": self.sensitivity,
                "parameters_used": adapted_params
            }

        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            return {"error": str(e)}

    def batch_process(self, input_dir: str, output_dir: str = None, file_extensions: List[str] = None) -> Dict:
        """Process multiple images in a directory"""
        try:
            if file_extensions is None:
                file_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']

            input_path = Path(input_dir)
            if not input_path.exists():
                raise ValueError(f"Input directory does not exist: {input_dir}")

            # Create output directory
            if output_dir is None:
                output_dir = input_path / "cropped_universal"
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)

            # Find all image files
            image_files = []
            for ext in file_extensions:
                image_files.extend(input_path.glob(f"*{ext}"))
                image_files.extend(input_path.glob(f"*{ext.upper()}"))

            if not image_files:
                logger.warning(f"No image files found in {input_dir}")
                return {"processed": 0, "failed": 0, "results": []}

            logger.info(f"Found {len(image_files)} images to process")

            # Process each image
            results = []
            processed = 0
            failed = 0

            for i, img_file in enumerate(image_files, 1):
                try:
                    logger.info(f"Processing {i}/{len(image_files)}: {img_file.name}")

                    # Generate output path
                    output_file = output_path / f"{img_file.stem}_universal_cropped{img_file.suffix}"

                    # Process image
                    result_path = self.process_image(str(img_file), str(output_file))

                    results.append({
                        "input": str(img_file),
                        "output": result_path,
                        "status": "success"
                    })
                    processed += 1

                except Exception as e:
                    logger.error(f"Failed to process {img_file}: {e}")
                    results.append({
                        "input": str(img_file),
                        "output": None,
                        "status": "failed",
                        "error": str(e)
                    })
                    failed += 1

            # Save batch report
            report = {
                "processed": processed,
                "failed": failed,
                "total": len(image_files),
                "sensitivity": self.sensitivity,
                "output_directory": str(output_path),
                "results": results
            }

            report_file = output_path / f"batch_report_{int(time.time())}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            logger.info(f"✅ Batch processing completed!")
            logger.info(f"📊 Processed: {processed}, Failed: {failed}")
            logger.info(f"📁 Report saved: {report_file}")

            return report

        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            raise


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="Universal Edge Cropper - Remove padding from images of any size",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage
  python universal_edge_cropper.py input.jpg

  # High sensitivity cropping
  python universal_edge_cropper.py input.jpg -s high

  # Batch process directory
  python universal_edge_cropper.py /path/to/images --batch

  # Analyze padding only
  python universal_edge_cropper.py input.jpg --analyze-only

  # Custom output path
  python universal_edge_cropper.py input.jpg -o output.jpg
        """
    )

    parser.add_argument('input', help='Input image file or directory (for batch mode)')
    parser.add_argument('-o', '--output', help='Output image path (for single image)')
    parser.add_argument('-s', '--sensitivity', choices=['low', 'medium', 'high', 'ultra'],
                       default='medium', help='Detection sensitivity level')
    parser.add_argument('-t', '--threshold', type=int, default=30,
                       help='Base threshold for black pixel detection')
    parser.add_argument('--batch', action='store_true',
                       help='Process all images in input directory')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze padding without processing')
    parser.add_argument('--output-dir', help='Output directory for batch processing')
    parser.add_argument('--no-adaptive', action='store_true',
                       help='Disable adaptive parameter adjustment')

    args = parser.parse_args()

    try:
        # Create cropper instance
        cropper = UniversalEdgeCropper(
            base_threshold=args.threshold,
            adaptive_mode=not args.no_adaptive,
            sensitivity=args.sensitivity
        )

        if args.analyze_only:
            # Analyze image padding
            analysis = cropper.analyze_image_padding(args.input)
            print("\n" + "="*70)
            print("UNIVERSAL EDGE PADDING ANALYSIS")
            print("="*70)
            for key, value in analysis.items():
                if key != "parameters_used":
                    print(f"{key.replace('_', ' ').title()}: {value}")
            print("="*70)

        elif args.batch:
            # Batch process directory
            report = cropper.batch_process(args.input, args.output_dir)
            print(f"\n✅ Batch processing completed!")
            print(f"📊 Results: {report['processed']} processed, {report['failed']} failed")

        else:
            # Process single image
            result = cropper.process_image(args.input, args.output)
            print(f"\n✅ Successfully processed: {result}")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
