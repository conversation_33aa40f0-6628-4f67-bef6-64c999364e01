{"processed": 16, "failed": 0, "total": 16, "sensitivity": "high", "output_directory": "universal_test_output", "results": [{"input": "sample_images\\bird_close_up.jpg", "output": "universal_test_output\\bird_close_up_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\bird_in_flight.jpg", "output": "universal_test_output\\bird_in_flight_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\bird_silhouette.jpg", "output": "universal_test_output\\bird_silhouette_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\centered_bird.jpg", "output": "universal_test_output\\centered_bird_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\complex_background_bird.jpg", "output": "universal_test_output\\complex_background_bird_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\multiple_birds.jpg", "output": "universal_test_output\\multiple_birds_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\off_center_bird.jpg", "output": "universal_test_output\\off_center_bird_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\small_bird_large_background.jpg", "output": "universal_test_output\\small_bird_large_background_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\bird_close_up.jpg", "output": "universal_test_output\\bird_close_up_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\bird_in_flight.jpg", "output": "universal_test_output\\bird_in_flight_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\bird_silhouette.jpg", "output": "universal_test_output\\bird_silhouette_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\centered_bird.jpg", "output": "universal_test_output\\centered_bird_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\complex_background_bird.jpg", "output": "universal_test_output\\complex_background_bird_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\multiple_birds.jpg", "output": "universal_test_output\\multiple_birds_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\off_center_bird.jpg", "output": "universal_test_output\\off_center_bird_universal_cropped.jpg", "status": "success"}, {"input": "sample_images\\small_bird_large_background.jpg", "output": "universal_test_output\\small_bird_large_background_universal_cropped.jpg", "status": "success"}]}