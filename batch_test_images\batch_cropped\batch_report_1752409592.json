{"processed": 8, "failed": 0, "total": 8, "sensitivity": "high", "output_directory": "batch_test_images\\batch_cropped", "results": [{"input": "batch_test_images\\batch_test_1.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_1_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_2.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_2_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_3.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_3_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_4.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_4_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_1.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_1_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_2.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_2_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_3.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_3_universal_cropped.jpg", "status": "success"}, {"input": "batch_test_images\\batch_test_4.jpg", "output": "batch_test_images\\batch_cropped\\batch_test_4_universal_cropped.jpg", "status": "success"}]}