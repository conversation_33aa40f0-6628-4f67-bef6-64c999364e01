#!/usr/bin/env python3
"""
Test Universal Edge Cropper
===========================

This script creates test images with black padding and demonstrates
the Universal Edge Cropper's ability to remove padding from images
of any size while preserving all content pixels.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
import os
from pathlib import Path
import time
from universal_edge_cropper import UniversalEdgeCropper

def create_test_image_with_padding(content_size=(400, 300), padding=(50, 50, 50, 50), output_path="test_with_padding.jpg"):
    """
    Create a test image with black padding around colorful content
    
    Args:
        content_size: (width, height) of the content area
        padding: (top, bottom, left, right) padding in pixels
        output_path: Path to save the test image
    """
    try:
        content_width, content_height = content_size
        top_pad, bottom_pad, left_pad, right_pad = padding
        
        # Calculate total image size
        total_width = content_width + left_pad + right_pad
        total_height = content_height + top_pad + bottom_pad
        
        # Create black image
        image = np.zeros((total_height, total_width, 3), dtype=np.uint8)
        
        # Create colorful content area
        content_area = image[top_pad:top_pad+content_height, left_pad:left_pad+content_width]
        
        # Fill with gradient colors
        for y in range(content_height):
            for x in range(content_width):
                # Create a colorful gradient pattern
                r = int(255 * (x / content_width))
                g = int(255 * (y / content_height))
                b = int(255 * ((x + y) / (content_width + content_height)))
                content_area[y, x] = [b, g, r]  # BGR format
        
        # Add some geometric shapes for more interesting content
        center_x, center_y = content_width // 2, content_height // 2
        
        # Draw a circle
        cv2.circle(content_area, (center_x, center_y), min(content_width, content_height) // 6, (255, 255, 255), -1)
        
        # Draw some rectangles
        cv2.rectangle(content_area, (10, 10), (50, 50), (0, 255, 255), -1)
        cv2.rectangle(content_area, (content_width-60, content_height-60), (content_width-10, content_height-10), (255, 0, 255), -1)
        
        # Save the image
        cv2.imwrite(output_path, image)
        
        print(f"✅ Created test image: {output_path}")
        print(f"   Total size: {total_width}x{total_height}")
        print(f"   Content size: {content_width}x{content_height}")
        print(f"   Padding: Top={top_pad}, Bottom={bottom_pad}, Left={left_pad}, Right={right_pad}")
        
        return output_path, (content_width, content_height), padding
        
    except Exception as e:
        print(f"❌ Error creating test image: {e}")
        return None, None, None

def test_universal_cropper_with_different_sizes():
    """Test the universal cropper with images of different sizes"""
    
    print("🧪 Testing Universal Edge Cropper with Different Image Sizes")
    print("=" * 70)
    
    # Create test directory
    test_dir = Path("universal_cropper_tests")
    test_dir.mkdir(exist_ok=True)
    
    # Test configurations: (content_size, padding, description)
    test_configs = [
        ((200, 150), (25, 25, 25, 25), "Small image with uniform padding"),
        ((800, 600), (100, 50, 75, 125), "Medium image with asymmetric padding"),
        ((1920, 1080), (200, 150, 100, 300), "Large image with heavy padding"),
        ((100, 100), (10, 20, 15, 5), "Tiny image with minimal padding"),
        ((2560, 1440), (50, 50, 50, 50), "Ultra-wide image with uniform padding"),
        ((400, 1200), (80, 120, 60, 40), "Tall portrait with varied padding"),
        ((1200, 400), (40, 60, 120, 80), "Wide landscape with varied padding"),
    ]
    
    # Create cropper instances with different sensitivities
    croppers = {
        'medium': UniversalEdgeCropper(sensitivity='medium'),
        'high': UniversalEdgeCropper(sensitivity='high'),
        'ultra': UniversalEdgeCropper(sensitivity='ultra')
    }
    
    results = []
    
    for i, (content_size, padding, description) in enumerate(test_configs, 1):
        print(f"\n🔍 Test {i}: {description}")
        print("-" * 50)
        
        # Create test image
        test_image_path = test_dir / f"test_{i}_original.jpg"
        created_path, actual_content_size, actual_padding = create_test_image_with_padding(
            content_size, padding, str(test_image_path)
        )
        
        if not created_path:
            continue
        
        # Test with different sensitivity levels
        for sensitivity, cropper in croppers.items():
            try:
                print(f"\n  📊 Testing with {sensitivity.upper()} sensitivity:")
                
                # Analyze first
                analysis = cropper.analyze_image_padding(created_path)
                
                # Process the image
                output_path = test_dir / f"test_{i}_cropped_{sensitivity}.jpg"
                result_path = cropper.process_image(created_path, str(output_path))
                
                # Verify results
                expected_width, expected_height = actual_content_size
                detected_width = analysis['final_size'].split('x')[0]
                detected_height = analysis['final_size'].split('x')[1]
                
                accuracy = "✅ PERFECT" if (int(detected_width) == expected_width and 
                                         int(detected_height) == expected_height) else "⚠️  APPROXIMATE"
                
                print(f"    Expected content: {expected_width}x{expected_height}")
                print(f"    Detected content: {detected_width}x{detected_height}")
                print(f"    Accuracy: {accuracy}")
                print(f"    Padding removed: {analysis['size_reduction_percent']}%")
                
                results.append({
                    'test': i,
                    'description': description,
                    'sensitivity': sensitivity,
                    'expected_size': f"{expected_width}x{expected_height}",
                    'detected_size': analysis['final_size'],
                    'accuracy': accuracy,
                    'reduction_percent': analysis['size_reduction_percent']
                })
                
            except Exception as e:
                print(f"    ❌ Error with {sensitivity} sensitivity: {e}")
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 UNIVERSAL CROPPER TEST SUMMARY")
    print("=" * 70)
    
    perfect_results = [r for r in results if "PERFECT" in r['accuracy']]
    total_tests = len(results)
    
    print(f"Total tests run: {total_tests}")
    print(f"Perfect results: {len(perfect_results)}")
    print(f"Success rate: {len(perfect_results)/total_tests*100:.1f}%")
    
    print("\n📈 Results by sensitivity:")
    for sensitivity in ['medium', 'high', 'ultra']:
        sens_results = [r for r in results if r['sensitivity'] == sensitivity]
        sens_perfect = [r for r in sens_results if "PERFECT" in r['accuracy']]
        if sens_results:
            print(f"  {sensitivity.upper()}: {len(sens_perfect)}/{len(sens_results)} perfect ({len(sens_perfect)/len(sens_results)*100:.1f}%)")
    
    print(f"\n📁 Test images and results saved in: {test_dir}")
    print("=" * 70)
    
    return results

def demonstrate_batch_processing():
    """Demonstrate batch processing capabilities"""
    
    print("\n🔄 Demonstrating Batch Processing")
    print("=" * 50)
    
    # Create a batch of test images
    batch_dir = Path("batch_test_images")
    batch_dir.mkdir(exist_ok=True)
    
    # Create various test images
    test_images = [
        ((300, 200), (30, 30, 30, 30), "batch_test_1.jpg"),
        ((500, 400), (50, 25, 75, 100), "batch_test_2.jpg"),
        ((800, 600), (80, 60, 40, 120), "batch_test_3.jpg"),
        ((400, 800), (40, 80, 60, 20), "batch_test_4.jpg"),
    ]
    
    print("Creating test images for batch processing...")
    for content_size, padding, filename in test_images:
        create_test_image_with_padding(content_size, padding, str(batch_dir / filename))
    
    # Run batch processing
    cropper = UniversalEdgeCropper(sensitivity='high')
    output_dir = batch_dir / "batch_cropped"
    
    print(f"\nRunning batch processing on {len(test_images)} images...")
    report = cropper.batch_process(str(batch_dir), str(output_dir))
    
    print(f"✅ Batch processing completed!")
    print(f"   Processed: {report['processed']}")
    print(f"   Failed: {report['failed']}")
    print(f"   Output directory: {report['output_directory']}")

def main():
    """Main test function"""
    print("🚀 Universal Edge Cropper - Comprehensive Test Suite")
    print("=" * 70)
    
    try:
        # Test with different image sizes
        results = test_universal_cropper_with_different_sizes()
        
        # Demonstrate batch processing
        demonstrate_batch_processing()
        
        print("\n🎉 All tests completed successfully!")
        print("The Universal Edge Cropper is ready for use with images of any size!")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
